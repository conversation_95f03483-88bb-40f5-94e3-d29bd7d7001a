-- API Key表结构
CREATE TABLE `li_api_key` (
  `id` varchar(255) NOT NULL COMMENT 'ID',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `delete_flag` bit(1) DEFAULT NULL COMMENT '删除标志 true/false 删除/未删除',
  `update_by` varchar(255) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `name` varchar(255) NOT NULL COMMENT 'API Key名称',
  `key_value` varchar(255) NOT NULL COMMENT 'API Key值',
  `secret` varchar(255) NOT NULL COMMENT 'API Key密钥',
  `status` varchar(50) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-启用，DISABLED-禁用，EXPIRED-过期',
  `description` text COMMENT '描述',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `usage_count` bigint DEFAULT '0' COMMENT '使用次数',
  `allowed_ips` text COMMENT '允许的IP地址，多个用逗号分隔',
  `permissions` text COMMENT '权限范围，JSON格式',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_key_value` (`key_value`),
  KEY `idx_status` (`status`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='API Key表';

-- 插入示例数据
INSERT INTO `li_api_key` (`id`, `create_by`, `create_time`, `delete_flag`, `update_by`, `update_time`, `name`, `key_value`, `secret`, `status`, `description`, `expire_time`, `last_used_time`, `usage_count`, `allowed_ips`, `permissions`) VALUES
('1', 'admin', NOW(), b'0', 'admin', NOW(), '微信小程序积分更新', 'ak_wechat_miniprogram_001', 'e10adc3949ba59abbe56e057f20f883e', 'ACTIVE', '用于微信小程序调用积分更新接口', NULL, NULL, 0, NULL, '{"social_points:update": true, "social_points:query": true}');
