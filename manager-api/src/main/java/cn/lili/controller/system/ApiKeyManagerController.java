package cn.lili.controller.system;

import cn.lili.common.enums.ResultCode;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.vo.PageVO;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.system.entity.dos.ApiKey;
import cn.lili.modules.system.entity.vo.ApiKeyVO;
import cn.lili.modules.system.service.ApiKeyService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 管理端API Key管理接口
 *
 * <AUTHOR>
 * @since 2024/12/20
 */
@Slf4j
@RestController
@Api(tags = "管理端API Key管理接口")
@RequestMapping("/manager/system/apiKey")
public class ApiKeyManagerController {

    @Autowired
    private ApiKeyService apiKeyService;

    @ApiOperation(value = "分页获取API Key列表")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "name", value = "API Key名称", dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "status", value = "状态", dataType = "String", paramType = "query")
    })
    @GetMapping
    public ResultMessage<IPage<ApiKeyVO>> getApiKeyPage(PageVO page,
                                                        @RequestParam(required = false) String name,
                                                        @RequestParam(required = false) String status) {
        return ResultUtil.data(apiKeyService.getApiKeyPage(page, name, status));
    }

    @ApiOperation(value = "创建API Key")
    @PostMapping
    public ResultMessage<ApiKey> createApiKey(@Valid @RequestBody ApiKey apiKey) {
        log.info("创建API Key: {}", apiKey.getName());
        return ResultUtil.data(apiKeyService.createApiKey(apiKey));
    }

    @ApiOperation(value = "获取API Key详情")
    @ApiImplicitParam(name = "id", value = "API Key ID", required = true, dataType = "String", paramType = "path")
    @GetMapping("/{id}")
    public ResultMessage<ApiKey> getApiKeyById(@PathVariable String id) {
        return ResultUtil.data(apiKeyService.getById(id));
    }

    @ApiOperation(value = "更新API Key")
    @ApiImplicitParam(name = "id", value = "API Key ID", required = true, dataType = "String", paramType = "path")
    @PutMapping("/{id}")
    public ResultMessage<Boolean> updateApiKey(@PathVariable String id, @Valid @RequestBody ApiKey apiKey) {
        apiKey.setId(id);
        log.info("更新API Key: {}", id);
        return ResultUtil.data(apiKeyService.updateById(apiKey));
    }

    @ApiOperation(value = "删除API Key")
    @ApiImplicitParam(name = "id", value = "API Key ID", required = true, dataType = "String", paramType = "path")
    @DeleteMapping("/{id}")
    public ResultMessage<Boolean> deleteApiKey(@PathVariable String id) {
        log.info("删除API Key: {}", id);
        return ResultUtil.data(apiKeyService.removeById(id));
    }

    @ApiOperation(value = "启用API Key")
    @ApiImplicitParam(name = "id", value = "API Key ID", required = true, dataType = "String", paramType = "path")
    @PostMapping("/{id}/enable")
    public ResultMessage<Void> enableApiKey(@PathVariable String id) {
        log.info("启用API Key: {}", id);
        apiKeyService.enableApiKey(id);
        return ResultUtil.success();
    }

    @ApiOperation(value = "禁用API Key")
    @ApiImplicitParam(name = "id", value = "API Key ID", required = true, dataType = "String", paramType = "path")
    @PostMapping("/{id}/disable")
    public ResultMessage<Void> disableApiKey(@PathVariable String id) {
        log.info("禁用API Key: {}", id);
        apiKeyService.disableApiKey(id);
        return ResultUtil.success();
    }

    @ApiOperation(value = "获取API Key的完整密钥信息")
    @ApiImplicitParam(name = "id", value = "API Key ID", required = true, dataType = "String", paramType = "path")
    @GetMapping("/{id}/secret")
    public ResultMessage<ApiKeyVO> getApiKeySecret(@PathVariable String id) {
        ApiKey apiKey = apiKeyService.getById(id);
        if (apiKey == null) {
            return ResultUtil.error(ResultCode.APIKEY_NOT_EXIST);
        }
        
        ApiKeyVO vo = new ApiKeyVO();
        vo.setId(apiKey.getId());
        vo.setName(apiKey.getName());
        vo.setKeyValue(apiKey.getKeyValue());
        vo.setSecret(apiKey.getSecret());
        vo.setShowSecret(true); // 显示真实密钥
        vo.setStatus(apiKey.getStatus());
        vo.setDescription(apiKey.getDescription());
        vo.setExpireTime(apiKey.getExpireTime());
        vo.setLastUsedTime(apiKey.getLastUsedTime());
        vo.setUsageCount(apiKey.getUsageCount());
        vo.setAllowedIps(apiKey.getAllowedIps());
        vo.setPermissions(apiKey.getPermissions());
        vo.setCreateTime(apiKey.getCreateTime());
        vo.setUpdateTime(apiKey.getUpdateTime());
        
        return ResultUtil.data(vo);
    }
}
