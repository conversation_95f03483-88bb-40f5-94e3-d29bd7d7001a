# API Key 积分更新接口使用指南

## 概述

为了支持第三方微信小程序通过API Key方式调用积分更新接口，我们新增了专门的API Key认证机制。这样第三方应用无需获取用户的accessToken，只需要使用预先分配的API Key即可调用积分相关接口。

## 新增接口

### 1. 通过API Key更新指定微信用户的积分

**接口地址：** `POST /buyer/api/member/socialPoints/updatePointsByWxUnionId/{wxUnionId}`

**请求头：**
```
X-API-Key: your_api_key_here
X-API-Secret: your_api_secret_here (可选)
Content-Type: application/json
```

**路径参数：**
- `wxUnionId`: 微信用户的unionId

**请求体：**
```json
{
  "changePoint": "100.00",
  "reason": "完成任务奖励"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": {
    "point": "1100.00",
    "totalPoint": "1100.00",
    "freezePoint": "0.00"
  }
}
```

### 2. 通过API Key批量更新积分（包含unionId）

**接口地址：** `POST /buyer/api/member/socialPoints/updatePointsWithUnionId`

**请求头：**
```
X-API-Key: your_api_key_here
X-API-Secret: your_api_secret_here (可选)
Content-Type: application/json
```

**请求体：**
```json
{
  "wxUnionId": "user_union_id_here",
  "changePoint": "50.00",
  "reason": "签到奖励"
}
```

### 3. 通过API Key查询指定微信用户的积分

**接口地址：** `GET /buyer/api/member/socialPoints/getMemberPointsByWx/{wxUnionId}`

**请求头：**
```
X-API-Key: your_api_key_here
X-API-Secret: your_api_secret_here (可选)
```

**路径参数：**
- `wxUnionId`: 微信用户的unionId

## API Key管理

### 创建API Key

管理员可以通过管理端接口创建API Key：

**接口地址：** `POST /manager/system/apiKey`

**请求体：**
```json
{
  "name": "微信小程序积分更新",
  "description": "用于微信小程序调用积分更新接口",
  "permissions": "{\"social_points:update\": true, \"social_points:query\": true}",
  "allowedIps": "*************,*************",
  "expireTime": "2025-12-31 23:59:59"
}
```

### 权限配置

API Key支持细粒度的权限控制，目前支持的权限包括：

- `social_points:update`: 积分更新权限
- `social_points:query`: 积分查询权限

权限配置格式为JSON：
```json
{
  "social_points:update": true,
  "social_points:query": true
}
```

## 安全特性

1. **API Key验证**: 每个请求都需要提供有效的API Key
2. **API Secret验证**: 可选的额外安全层，提供双重验证
3. **IP白名单**: 可以限制API Key只能从特定IP地址访问
4. **权限控制**: 细粒度的权限控制，确保API Key只能访问授权的功能
5. **使用统计**: 记录API Key的使用次数和最后使用时间
6. **过期控制**: 支持设置API Key的过期时间

## 错误码说明

- `400`: 请求参数错误（如缺少wxUnionId）
- `401`: API Key认证失败
- `403`: API Key没有相应权限或IP地址不被允许
- `404`: 用户不存在
- `500`: 服务器内部错误

## 使用示例

### JavaScript/Node.js 示例

```javascript
const axios = require('axios');

const apiClient = axios.create({
  baseURL: 'https://buyer-api.dboss.pro',
  headers: {
    'X-API-Key': 'ak_wechat_miniprogram_001',
    'X-API-Secret': 'your_secret_here',
    'Content-Type': 'application/json'
  }
});

// 更新用户积分
async function updateUserPoints(wxUnionId, changePoint, reason) {
  try {
    const response = await apiClient.post(
      `/buyer/api/member/socialPoints/updatePointsByWxUnionId/${wxUnionId}`,
      {
        changePoint: changePoint.toString(),
        reason: reason
      }
    );
    
    console.log('积分更新成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('积分更新失败:', error.response?.data || error.message);
    throw error;
  }
}

// 查询用户积分
async function getUserPoints(wxUnionId) {
  try {
    const response = await apiClient.get(
      `/buyer/api/member/socialPoints/getMemberPointsByWx/${wxUnionId}`
    );
    
    console.log('积分查询成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('积分查询失败:', error.response?.data || error.message);
    throw error;
  }
}

// 使用示例
updateUserPoints('user_union_id_123', 100, '完成任务奖励');
getUserPoints('user_union_id_123');
```

### Python 示例

```python
import requests
import json

class SocialPointsAPI:
    def __init__(self, base_url, api_key, api_secret=None):
        self.base_url = base_url
        self.headers = {
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        }
        if api_secret:
            self.headers['X-API-Secret'] = api_secret
    
    def update_points(self, wx_union_id, change_point, reason):
        url = f"{self.base_url}/buyer/api/member/socialPoints/updatePointsByWxUnionId/{wx_union_id}"
        data = {
            'changePoint': str(change_point),
            'reason': reason
        }
        
        response = requests.post(url, headers=self.headers, json=data)
        response.raise_for_status()
        return response.json()
    
    def get_points(self, wx_union_id):
        url = f"{self.base_url}/buyer/api/member/socialPoints/getMemberPointsByWx/{wx_union_id}"
        
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        return response.json()

# 使用示例
api = SocialPointsAPI(
    base_url='https://buyer-api.dboss.pro',
    api_key='ak_wechat_miniprogram_001',
    api_secret='your_secret_here'
)

# 更新积分
result = api.update_points('user_union_id_123', 100, '完成任务奖励')
print('积分更新结果:', result)

# 查询积分
points = api.get_points('user_union_id_123')
print('用户积分:', points)
```

## 注意事项

1. **API Key安全**: 请妥善保管API Key和Secret，不要在客户端代码中硬编码
2. **权限最小化**: 为API Key分配最小必要权限
3. **IP限制**: 建议配置IP白名单以增强安全性
4. **监控使用**: 定期检查API Key的使用情况，发现异常及时处理
5. **定期轮换**: 建议定期更换API Key以提高安全性

## 数据库表结构

系统会自动创建`li_api_key`表来存储API Key信息。如需手动创建，请参考`DB/api_key_table.sql`文件。
