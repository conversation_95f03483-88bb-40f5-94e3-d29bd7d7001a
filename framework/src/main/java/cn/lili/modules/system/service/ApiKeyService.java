package cn.lili.modules.system.service;

import cn.lili.common.vo.PageVO;
import cn.lili.modules.system.entity.dos.ApiKey;
import cn.lili.modules.system.entity.vo.ApiKeyVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * API Key业务层
 *
 * <AUTHOR>
 * @since 2024/12/20
 */
public interface ApiKeyService extends IService<ApiKey> {

    /**
     * 验证API Key是否有效
     *
     * @param keyValue API Key值
     * @return 是否有效
     */
    boolean validateApiKey(String keyValue);

    /**
     * 根据API Key值获取API Key信息
     *
     * @param keyValue API Key值
     * @return API Key信息
     */
    ApiKey getByKeyValue(String keyValue);

    /**
     * 创建API Key
     *
     * @param apiKey API Key信息
     * @return 创建的API Key
     */
    ApiKey createApiKey(ApiKey apiKey);

    /**
     * 分页查询API Key
     *
     * @param page 分页参数
     * @param name 名称（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    IPage<ApiKeyVO> getApiKeyPage(PageVO page, String name, String status);

    /**
     * 更新API Key使用信息
     *
     * @param keyValue API Key值
     */
    void updateUsageInfo(String keyValue);

    /**
     * 禁用API Key
     *
     * @param id API Key ID
     */
    void disableApiKey(String id);

    /**
     * 启用API Key
     *
     * @param id API Key ID
     */
    void enableApiKey(String id);

    /**
     * 检查API Key权限
     *
     * @param keyValue API Key值
     * @param permission 权限标识
     * @return 是否有权限
     */
    boolean hasPermission(String keyValue, String permission);
}
