package cn.lili.modules.system.entity.vo;

import cn.lili.modules.system.entity.dos.ApiKey;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * API Key VO
 *
 * <AUTHOR>
 * @since 2024/12/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "API Key VO")
public class ApiKeyVO extends ApiKey {

    @ApiModelProperty(value = "是否显示密钥")
    private Boolean showSecret = false;

    /**
     * 重写getSecret方法，根据showSecret决定是否返回真实密钥
     */
    @Override
    public String getSecret() {
        if (showSecret != null && showSecret) {
            return super.getSecret();
        }
        return "******";
    }
}
