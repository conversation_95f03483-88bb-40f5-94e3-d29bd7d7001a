package cn.lili.modules.system.entity.dos;

import cn.lili.mybatis.BaseEntity;
import cn.lili.modules.system.entity.enums.ApiKeyStatusEnum;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * API Key实体
 *
 * <AUTHOR>
 * @since 2024/12/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("li_api_key")
@ApiModel(value = "API Key")
public class ApiKey extends BaseEntity {

    @ApiModelProperty(value = "API Key名称")
    private String name;

    @ApiModelProperty(value = "API Key值")
    private String keyValue;

    @ApiModelProperty(value = "API Key密钥")
    private String secret;

    @ApiModelProperty(value = "状态")
    private String status = ApiKeyStatusEnum.ACTIVE.name();

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "过期时间")
    private Date expireTime;

    @ApiModelProperty(value = "最后使用时间")
    private Date lastUsedTime;

    @ApiModelProperty(value = "使用次数")
    private Long usageCount = 0L;

    @ApiModelProperty(value = "允许的IP地址，多个用逗号分隔")
    private String allowedIps;

    @ApiModelProperty(value = "权限范围，JSON格式")
    private String permissions;
}
