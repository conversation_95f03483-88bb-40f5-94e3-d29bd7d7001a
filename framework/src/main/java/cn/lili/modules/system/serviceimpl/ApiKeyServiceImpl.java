package cn.lili.modules.system.serviceimpl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.system.entity.dos.ApiKey;
import cn.lili.modules.system.entity.enums.ApiKeyStatusEnum;
import cn.lili.modules.system.entity.vo.ApiKeyVO;
import cn.lili.modules.system.mapper.ApiKeyMapper;
import cn.lili.modules.system.service.ApiKeyService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * API Key业务层实现
 *
 * <AUTHOR>
 * @since 2024/12/20
 */
@Slf4j
@Service
public class ApiKeyServiceImpl extends ServiceImpl<ApiKeyMapper, ApiKey> implements ApiKeyService {

    @Override
    public boolean validateApiKey(String keyValue) {
        if (CharSequenceUtil.isEmpty(keyValue)) {
            return false;
        }

        ApiKey apiKey = getByKeyValue(keyValue);
        if (apiKey == null) {
            return false;
        }

        // 检查状态
        if (!ApiKeyStatusEnum.ACTIVE.name().equals(apiKey.getStatus())) {
            return false;
        }

        // 检查过期时间
        if (apiKey.getExpireTime() != null && apiKey.getExpireTime().before(new Date())) {
            // 自动更新状态为过期
            apiKey.setStatus(ApiKeyStatusEnum.EXPIRED.name());
            updateById(apiKey);
            return false;
        }

        return true;
    }

    @Override
    public ApiKey getByKeyValue(String keyValue) {
        if (CharSequenceUtil.isEmpty(keyValue)) {
            return null;
        }

        LambdaQueryWrapper<ApiKey> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApiKey::getKeyValue, keyValue);
        return getOne(queryWrapper);
    }

    @Override
    public ApiKey createApiKey(ApiKey apiKey) {
        // 生成API Key值
        if (CharSequenceUtil.isEmpty(apiKey.getKeyValue())) {
            apiKey.setKeyValue("ak_" + IdUtil.simpleUUID());
        }

        // 生成密钥
        if (CharSequenceUtil.isEmpty(apiKey.getSecret())) {
            apiKey.setSecret(SecureUtil.md5(IdUtil.fastUUID()));
        }

        // 设置默认状态
        if (CharSequenceUtil.isEmpty(apiKey.getStatus())) {
            apiKey.setStatus(ApiKeyStatusEnum.ACTIVE.name());
        }

        // 初始化使用次数
        if (apiKey.getUsageCount() == null) {
            apiKey.setUsageCount(0L);
        }

        save(apiKey);
        return apiKey;
    }

    @Override
    public IPage<ApiKeyVO> getApiKeyPage(PageVO page, String name, String status) {
        LambdaQueryWrapper<ApiKey> queryWrapper = new LambdaQueryWrapper<>();
        
        if (CharSequenceUtil.isNotEmpty(name)) {
            queryWrapper.like(ApiKey::getName, name);
        }
        
        if (CharSequenceUtil.isNotEmpty(status)) {
            queryWrapper.eq(ApiKey::getStatus, status);
        }
        
        queryWrapper.orderByDesc(ApiKey::getCreateTime);
        
        IPage<ApiKey> apiKeyPage = page(new Page<>(page.getPageNumber(), page.getPageSize()), queryWrapper);
        
        // 转换为VO
        List<ApiKeyVO> voList = apiKeyPage.getRecords().stream().map(apiKey -> {
            ApiKeyVO vo = new ApiKeyVO();
            BeanUtils.copyProperties(apiKey, vo);
            return vo;
        }).collect(Collectors.toList());
        
        IPage<ApiKeyVO> voPage = new Page<>();
        BeanUtils.copyProperties(apiKeyPage, voPage);
        voPage.setRecords(voList);
        
        return voPage;
    }

    @Override
    public void updateUsageInfo(String keyValue) {
        ApiKey apiKey = getByKeyValue(keyValue);
        if (apiKey != null) {
            apiKey.setLastUsedTime(new Date());
            apiKey.setUsageCount(apiKey.getUsageCount() + 1);
            updateById(apiKey);
        }
    }

    @Override
    public void disableApiKey(String id) {
        ApiKey apiKey = getById(id);
        if (apiKey == null) {
            throw new ServiceException(ResultCode.USER_NOT_EXIST);
        }
        apiKey.setStatus(ApiKeyStatusEnum.DISABLED.name());
        updateById(apiKey);
    }

    @Override
    public void enableApiKey(String id) {
        ApiKey apiKey = getById(id);
        if (apiKey == null) {
            throw new ServiceException(ResultCode.USER_NOT_EXIST);
        }
        apiKey.setStatus(ApiKeyStatusEnum.ACTIVE.name());
        updateById(apiKey);
    }

    @Override
    public boolean hasPermission(String keyValue, String permission) {
        ApiKey apiKey = getByKeyValue(keyValue);
        if (apiKey == null || CharSequenceUtil.isEmpty(apiKey.getPermissions())) {
            return false;
        }

        try {
            Map<String, Object> permissions = JSONUtil.toBean(apiKey.getPermissions(), Map.class);
            return permissions.containsKey(permission) && Boolean.TRUE.equals(permissions.get(permission));
        } catch (Exception e) {
            log.error("解析API Key权限失败", e);
            return false;
        }
    }
}
