package cn.lili.modules.system.entity.enums;

/**
 * API Key状态枚举
 *
 * <AUTHOR>
 * @since 2024/12/20
 */
public enum ApiKeyStatusEnum {

    /**
     * 启用
     */
    ACTIVE("启用"),

    /**
     * 禁用
     */
    DISABLED("禁用"),

    /**
     * 过期
     */
    EXPIRED("过期");

    private final String description;

    ApiKeyStatusEnum(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
