package cn.lili.modules.member.serviceimpl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.connect.service.ConnectService;
import cn.lili.modules.member.entity.vo.MemberPointsHistoryVO;
import cn.lili.modules.member.entity.vo.SocialPointsHistoryVO;
import cn.lili.modules.member.entity.vo.SocialPointsVO;
import cn.lili.modules.member.service.MemberPointsHistoryService;
import cn.lili.modules.member.service.MemberService;
import cn.lili.modules.member.service.SocialPointsService;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.lili.modules.member.entity.dos.MemberPointsHistory;
import cn.lili.modules.member.entity.enums.PointTypeEnum;

/**
 * 会员积分历史业务层实现
 *
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
@Service
@Slf4j
public class SocialPointsServiceImpl implements SocialPointsService {

    @Autowired
    private ConnectService connectService;

    @Autowired
    private MemberPointsHistoryService memberPointsHistoryService;

    @Autowired
    private MemberService memberService;

    @Override
    public MemberPointsHistoryVO getMemberPointsHistoryVOByWxUnionId(String wxUnionId) {
        try {
            String memberId = connectService.getMemberIdByUnionId(wxUnionId);
            if (CharSequenceUtil.isEmpty(memberId)) {
                log.warn("No member found for wxUnionId: {}", wxUnionId);
                throw new ServiceException(ResultCode.USER_NOT_EXIST);
            }
            return memberPointsHistoryService.getMemberPointsHistoryVO(memberId);
        } catch (Exception e) {
            log.error("Error getting member points history for wxUnionId: {}", wxUnionId, e);
            throw new ServiceException(ResultCode.USER_POINTS_QUERY_ERROR);
        }
    }

    @Override
    public MemberPointsHistoryVO getCurrentUserPoints() {
        AuthUser authUser = UserContext.getCurrentUser();
        if (authUser == null) {
            log.error("获取用户积分失败，用户未登录");
            throw new ServiceException(ResultCode.USER_NOT_LOGIN);
        }
        String wxUnionId = connectService.getUnionIdByMemberId(authUser.getId());
        if (CharSequenceUtil.isEmpty(wxUnionId)) {
            log.error("获取用户积分失败，用户未绑定微信");
            throw new ServiceException(ResultCode.USER_NOT_BIND_WECHAT_ERROR);
        }
        return getMemberPointsHistoryVOByWxUnionId(wxUnionId);
    }

    @Override
    public MemberPointsHistoryVO updateCurrentUserPoints(SocialPointsVO socialPoints) {
        AuthUser authUser = UserContext.getCurrentUser();
        if (authUser == null) {
            log.error("更新用户积分失败，用户未登录");
            throw new ServiceException(ResultCode.USER_NOT_LOGIN);
        }
        String wxUnionId = connectService.getUnionIdByMemberId(authUser.getId());
        if (CharSequenceUtil.isEmpty(wxUnionId)) {
            log.error("更新用户积分失败，用户未绑定微信");
            throw new ServiceException(ResultCode.USER_NOT_BIND_WECHAT_ERROR);
        }
        socialPoints.setWxUnionId(wxUnionId);
        return updatePointsByWxUnionId(socialPoints);
    }

    @Override
    public IPage<SocialPointsHistoryVO> getMemberPointsHistoryByWx(PageVO page, String wxUnionId) {
        try {
            String memberId = connectService.getMemberIdByUnionId(wxUnionId);
            if (CharSequenceUtil.isEmpty(memberId)) {
                log.warn("No member found for wxUnionId: {} when fetching history", wxUnionId);
                throw new ServiceException(ResultCode.USER_NOT_EXIST);
            }

            IPage<MemberPointsHistory> memberHistoryPage = memberPointsHistoryService.MemberPointsHistoryList(page, memberId, null);

            List<SocialPointsHistoryVO> socialHistoryList = memberHistoryPage.getRecords().stream()
                    .map(this::convertToSocialPointsHistoryVO)
                    .collect(Collectors.toList());

            IPage<SocialPointsHistoryVO> resultPage = new Page<>(memberHistoryPage.getCurrent(), memberHistoryPage.getSize(), memberHistoryPage.getTotal());
            resultPage.setRecords(socialHistoryList);
            resultPage.setPages(memberHistoryPage.getPages());
            return resultPage;

        } catch (Exception e) {
            log.error("Error getting member points history page for wxUnionId: {}", wxUnionId, e);
            throw new ServiceException(ResultCode.USER_POINTS_QUERY_ERROR);
        }
    }

    private SocialPointsHistoryVO convertToSocialPointsHistoryVO(MemberPointsHistory memberPointsHistory) {
        SocialPointsHistoryVO vo = new SocialPointsHistoryVO();
        vo.setOldremain(memberPointsHistory.getBeforePoint());
        vo.setChange(memberPointsHistory.getVariablePoint());
        vo.setRemain(memberPointsHistory.getPoint());
        vo.setReason(memberPointsHistory.getContent());
        return vo;
    }


    @Override
    public IPage<SocialPointsHistoryVO> getCurrentUserPointsHistory(PageVO page) {
        AuthUser authUser = UserContext.getCurrentUser();
        if (authUser == null) {
            log.error("获取用户积分失败，用户未登录");
            throw new ServiceException(ResultCode.USER_NOT_LOGIN);
        }
        String wxUnionId = connectService.getUnionIdByMemberId(authUser.getId());
        if (CharSequenceUtil.isEmpty(wxUnionId)) {
            log.error("获取用户积分失败，用户未绑定微信");
            throw new ServiceException(ResultCode.USER_NOT_BIND_WECHAT_ERROR);
        }
        return getMemberPointsHistoryByWx(page, wxUnionId);
    }

    @Override
    public MemberPointsHistoryVO updatePointsByWxUnionId(SocialPointsVO socialPoints) {
        try {
            String memberId = connectService.getMemberIdByUnionId(socialPoints.getWxUnionId());
            if (CharSequenceUtil.isEmpty(memberId)) {
                log.warn("No member found for wxUnionId: {} during point update", socialPoints.getWxUnionId());
                throw new ServiceException(ResultCode.USER_NOT_EXIST);
            }

            BigDecimal changePoint = new BigDecimal(socialPoints.getChangePoint());
            String pointType = changePoint.compareTo(BigDecimal.ZERO) >= 0 ? PointTypeEnum.INCREASE.name() : PointTypeEnum.REDUCE.name();
            double pointValue = changePoint.abs().doubleValue();

            boolean success = memberService.updateMemberPoint(pointValue, pointType, memberId, socialPoints.getReason());

            if (success) {
                return memberPointsHistoryService.getMemberPointsHistoryVO(memberId);
            } else {
                log.error("Failed to update member points for memberId: {}", memberId);
                throw new ServiceException(ResultCode.USER_POINTS_UPDATE_ERROR);
            }
        } catch (Exception e) {
            log.error("Error updating points for wxUnionId: {}", socialPoints.getWxUnionId(), e);
            throw new ServiceException(ResultCode.USER_POINTS_UPDATE_ERROR);
        }
    }
}