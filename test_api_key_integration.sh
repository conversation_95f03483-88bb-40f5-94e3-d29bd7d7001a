#!/bin/bash

# API Key 积分更新接口集成测试脚本
# 使用方法: ./test_api_key_integration.sh

# 配置
BASE_URL="http://localhost:8888"
API_KEY="ak_wechat_miniprogram_001"
API_SECRET="e10adc3949ba59abbe56e057f20f883e"
TEST_UNION_ID="test_union_id_123"

echo "=== API Key 积分更新接口集成测试 ==="
echo "Base URL: $BASE_URL"
echo "API Key: $API_KEY"
echo "Test Union ID: $TEST_UNION_ID"
echo ""

# 测试1: 更新指定用户积分
echo "测试1: 更新指定用户积分"
echo "POST $BASE_URL/buyer/api/member/socialPoints/updatePointsByWxUnionId/$TEST_UNION_ID"

response1=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
  -X POST \
  -H "X-API-Key: $API_KEY" \
  -H "X-API-Secret: $API_SECRET" \
  -H "Content-Type: application/json" \
  -d '{
    "changePoint": "100.00",
    "reason": "API Key测试奖励"
  }' \
  "$BASE_URL/buyer/api/member/socialPoints/updatePointsByWxUnionId/$TEST_UNION_ID")

http_code1=$(echo "$response1" | grep "HTTP_CODE:" | cut -d: -f2)
response_body1=$(echo "$response1" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $http_code1"
echo "响应内容: $response_body1"
echo ""

# 测试2: 批量更新积分（包含unionId）
echo "测试2: 批量更新积分（包含unionId）"
echo "POST $BASE_URL/buyer/api/member/socialPoints/updatePointsWithUnionId"

response2=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
  -X POST \
  -H "X-API-Key: $API_KEY" \
  -H "X-API-Secret: $API_SECRET" \
  -H "Content-Type: application/json" \
  -d '{
    "wxUnionId": "'$TEST_UNION_ID'",
    "changePoint": "50.00",
    "reason": "API Key批量测试奖励"
  }' \
  "$BASE_URL/buyer/api/member/socialPoints/updatePointsWithUnionId")

http_code2=$(echo "$response2" | grep "HTTP_CODE:" | cut -d: -f2)
response_body2=$(echo "$response2" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $http_code2"
echo "响应内容: $response_body2"
echo ""

# 测试3: 查询用户积分
echo "测试3: 查询用户积分"
echo "GET $BASE_URL/buyer/api/member/socialPoints/getMemberPointsByWx/$TEST_UNION_ID"

response3=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
  -X GET \
  -H "X-API-Key: $API_KEY" \
  -H "X-API-Secret: $API_SECRET" \
  "$BASE_URL/buyer/api/member/socialPoints/getMemberPointsByWx/$TEST_UNION_ID")

http_code3=$(echo "$response3" | grep "HTTP_CODE:" | cut -d: -f2)
response_body3=$(echo "$response3" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $http_code3"
echo "响应内容: $response_body3"
echo ""

# 测试4: 无效API Key测试
echo "测试4: 无效API Key测试"
echo "POST $BASE_URL/buyer/api/member/socialPoints/updatePointsByWxUnionId/$TEST_UNION_ID"

response4=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
  -X POST \
  -H "X-API-Key: invalid_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "changePoint": "10.00",
    "reason": "无效API Key测试"
  }' \
  "$BASE_URL/buyer/api/member/socialPoints/updatePointsByWxUnionId/$TEST_UNION_ID")

http_code4=$(echo "$response4" | grep "HTTP_CODE:" | cut -d: -f2)
response_body4=$(echo "$response4" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $http_code4"
echo "响应内容: $response_body4"
echo ""

# 测试5: 缺少unionId测试
echo "测试5: 缺少unionId测试"
echo "POST $BASE_URL/buyer/api/member/socialPoints/updatePointsWithUnionId"

response5=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
  -X POST \
  -H "X-API-Key: $API_KEY" \
  -H "X-API-Secret: $API_SECRET" \
  -H "Content-Type: application/json" \
  -d '{
    "changePoint": "10.00",
    "reason": "缺少unionId测试"
  }' \
  "$BASE_URL/buyer/api/member/socialPoints/updatePointsWithUnionId")

http_code5=$(echo "$response5" | grep "HTTP_CODE:" | cut -d: -f2)
response_body5=$(echo "$response5" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $http_code5"
echo "响应内容: $response_body5"
echo ""

# 测试结果汇总
echo "=== 测试结果汇总 ==="
echo "测试1 - 更新指定用户积分: HTTP $http_code1"
echo "测试2 - 批量更新积分: HTTP $http_code2"
echo "测试3 - 查询用户积分: HTTP $http_code3"
echo "测试4 - 无效API Key: HTTP $http_code4 (期望401)"
echo "测试5 - 缺少unionId: HTTP $http_code5 (期望400)"
echo ""

# 检查测试结果
success_count=0
total_tests=5

if [ "$http_code1" = "200" ]; then
  echo "✓ 测试1通过"
  ((success_count++))
else
  echo "✗ 测试1失败"
fi

if [ "$http_code2" = "200" ]; then
  echo "✓ 测试2通过"
  ((success_count++))
else
  echo "✗ 测试2失败"
fi

if [ "$http_code3" = "200" ]; then
  echo "✓ 测试3通过"
  ((success_count++))
else
  echo "✗ 测试3失败"
fi

if [ "$http_code4" = "401" ]; then
  echo "✓ 测试4通过"
  ((success_count++))
else
  echo "✗ 测试4失败"
fi

if [ "$http_code5" = "400" ]; then
  echo "✓ 测试5通过"
  ((success_count++))
else
  echo "✗ 测试5失败"
fi

echo ""
echo "测试完成: $success_count/$total_tests 通过"

if [ $success_count -eq $total_tests ]; then
  echo "🎉 所有测试通过！"
  exit 0
else
  echo "❌ 部分测试失败，请检查实现"
  exit 1
fi
