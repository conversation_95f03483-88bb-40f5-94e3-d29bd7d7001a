package cn.lili.security;

import cn.hutool.core.util.StrUtil;
import cn.lili.common.utils.ResponseUtil;
import cn.lili.modules.system.entity.dos.ApiKey;
import cn.lili.modules.system.service.ApiKeyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * API Key认证过滤器
 *
 * <AUTHOR>
 * @since 2024/12/20
 */
@Slf4j
public class ApiKeyAuthenticationFilter extends BasicAuthenticationFilter {

    private static final String API_KEY_HEADER = "X-API-Key";
    private static final String API_SECRET_HEADER = "X-API-Secret";

    private final ApiKeyService apiKeyService;

    public ApiKeyAuthenticationFilter(AuthenticationManager authenticationManager, ApiKeyService apiKeyService) {
        super(authenticationManager);
        this.apiKeyService = apiKeyService;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) 
            throws IOException, ServletException {

        String apiKey = request.getHeader(API_KEY_HEADER);
        String apiSecret = request.getHeader(API_SECRET_HEADER);

        try {
            // 如果没有API Key，则跳过此过滤器
            if (StrUtil.isBlank(apiKey)) {
                chain.doFilter(request, response);
                return;
            }

            // 验证API Key
            UsernamePasswordAuthenticationToken authentication = getAuthentication(apiKey, apiSecret, request, response);
            if (authentication != null) {
                SecurityContextHolder.getContext().setAuthentication(authentication);
            }
        } catch (Exception e) {
            log.error("API Key认证异常:", e);
            ResponseUtil.output(response, 401, ResponseUtil.resultMap(false, 401, "API Key认证失败"));
            return;
        }

        chain.doFilter(request, response);
    }

    /**
     * 获取认证信息
     */
    private UsernamePasswordAuthenticationToken getAuthentication(String apiKey, String apiSecret, 
            HttpServletRequest request, HttpServletResponse response) {
        
        try {
            // 验证API Key是否有效
            if (!apiKeyService.validateApiKey(apiKey)) {
                log.warn("无效的API Key: {}", apiKey);
                ResponseUtil.output(response, 401, ResponseUtil.resultMap(false, 401, "无效的API Key"));
                return null;
            }

            // 获取API Key详细信息
            ApiKey apiKeyEntity = apiKeyService.getByKeyValue(apiKey);
            if (apiKeyEntity == null) {
                log.warn("API Key不存在: {}", apiKey);
                ResponseUtil.output(response, 401, ResponseUtil.resultMap(false, 401, "API Key不存在"));
                return null;
            }

            // 验证密钥（如果提供了的话）
            if (StrUtil.isNotBlank(apiSecret) && !apiSecret.equals(apiKeyEntity.getSecret())) {
                log.warn("API Secret不匹配: {}", apiKey);
                ResponseUtil.output(response, 401, ResponseUtil.resultMap(false, 401, "API Secret不匹配"));
                return null;
            }

            // 检查IP限制（如果配置了的话）
            if (StrUtil.isNotBlank(apiKeyEntity.getAllowedIps())) {
                String clientIp = getClientIpAddress(request);
                String[] allowedIps = apiKeyEntity.getAllowedIps().split(",");
                boolean ipAllowed = false;
                for (String allowedIp : allowedIps) {
                    if (allowedIp.trim().equals(clientIp)) {
                        ipAllowed = true;
                        break;
                    }
                }
                if (!ipAllowed) {
                    log.warn("IP地址不在允许列表中: {} for API Key: {}", clientIp, apiKey);
                    ResponseUtil.output(response, 403, ResponseUtil.resultMap(false, 403, "IP地址不被允许"));
                    return null;
                }
            }

            // 更新使用信息
            apiKeyService.updateUsageInfo(apiKey);

            // 构造认证信息
            List<GrantedAuthority> auths = new ArrayList<>();
            auths.add(new SimpleGrantedAuthority("ROLE_API_KEY"));
            
            UsernamePasswordAuthenticationToken authentication = 
                new UsernamePasswordAuthenticationToken(apiKey, null, auths);
            
            // 将API Key信息存储在details中
            authentication.setDetails(apiKeyEntity);
            
            return authentication;

        } catch (Exception e) {
            log.error("API Key认证处理异常:", e);
            return null;
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StrUtil.isNotBlank(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (StrUtil.isNotBlank(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
