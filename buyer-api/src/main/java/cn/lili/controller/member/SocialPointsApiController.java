package cn.lili.controller.member;

import cn.lili.common.enums.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.member.entity.vo.MemberPointsHistoryVO;
import cn.lili.modules.member.entity.vo.SocialPointsVO;
import cn.lili.modules.member.service.SocialPointsService;
import cn.lili.modules.system.entity.dos.ApiKey;
import cn.lili.modules.system.service.ApiKeyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * API Key方式调用的社交生态积分接口
 *
 * <AUTHOR>
 * @since 2024/12/20
 */
@Slf4j
@RestController
@Api(tags = "API Key方式调用的社交生态积分接口")
@RequestMapping("/buyer/api/member/socialPoints")
public class SocialPointsApiController {

    @Autowired
    private SocialPointsService socialPointsService;

    @Autowired
    private ApiKeyService apiKeyService;

    @ApiOperation(value = "通过API Key更新指定微信用户的积分")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "X-API-Key", value = "API Key", required = true, dataType = "String", paramType = "header"),
        @ApiImplicitParam(name = "X-API-Secret", value = "API Secret (可选)", required = false, dataType = "String", paramType = "header"),
        @ApiImplicitParam(name = "wxUnionId", value = "会员微信unionId", required = true, dataType = "String", paramType = "path"),
        @ApiImplicitParam(name = "socialPoints", value = "积分更新详情", required = true, dataTypeClass = SocialPointsVO.class, paramType = "body")
    })
    @PostMapping(value = "/updatePointsByWxUnionId/{wxUnionId}")
    public ResultMessage<MemberPointsHistoryVO> updatePointsByWxUnionId(
            @PathVariable String wxUnionId,
            @Valid @RequestBody SocialPointsVO socialPoints) {
        
        // 验证API Key权限
        if (!hasApiKeyPermission("social_points:update")) {
            return ResultUtil.error(403, "API Key没有积分更新权限");
        }

        // 确保使用路径中的 wxUnionId
        socialPoints.setWxUnionId(wxUnionId);
        
        log.info("API Key调用积分更新接口，wxUnionId: {}, changePoint: {}, reason: {}", 
                wxUnionId, socialPoints.getChangePoint(), socialPoints.getReason());
        
        return ResultUtil.data(socialPointsService.updatePointsByWxUnionId(socialPoints));
    }

    @ApiOperation(value = "通过API Key批量更新多个微信用户的积分")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "X-API-Key", value = "API Key", required = true, dataType = "String", paramType = "header"),
        @ApiImplicitParam(name = "X-API-Secret", value = "API Secret (可选)", required = false, dataType = "String", paramType = "header"),
        @ApiImplicitParam(name = "socialPoints", value = "积分更新详情，必须包含wxUnionId", required = true, dataTypeClass = SocialPointsVO.class, paramType = "body")
    })
    @PostMapping(value = "/updatePointsWithUnionId")
    public ResultMessage<MemberPointsHistoryVO> updatePointsWithUnionId(
            @Valid @RequestBody SocialPointsVO socialPoints) {
        
        // 验证API Key权限
        if (!hasApiKeyPermission("social_points:update")) {
            return ResultUtil.error(403, "API Key没有积分更新权限");
        }

        // 验证必须提供wxUnionId
        if (socialPoints.getWxUnionId() == null || socialPoints.getWxUnionId().trim().isEmpty()) {
            return ResultUtil.error(400, "wxUnionId不能为空");
        }
        
        log.info("API Key调用积分更新接口，wxUnionId: {}, changePoint: {}, reason: {}", 
                socialPoints.getWxUnionId(), socialPoints.getChangePoint(), socialPoints.getReason());
        
        return ResultUtil.data(socialPointsService.updatePointsByWxUnionId(socialPoints));
    }

    @ApiOperation(value = "通过API Key获取指定微信用户的积分信息")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "X-API-Key", value = "API Key", required = true, dataType = "String", paramType = "header"),
        @ApiImplicitParam(name = "X-API-Secret", value = "API Secret (可选)", required = false, dataType = "String", paramType = "header"),
        @ApiImplicitParam(name = "wxUnionId", value = "会员微信unionId", required = true, dataType = "String", paramType = "path")
    })
    @GetMapping(value = "/getMemberPointsByWx/{wxUnionId}")
    public ResultMessage<MemberPointsHistoryVO> getMemberPointsByWx(@PathVariable String wxUnionId) {
        
        // 验证API Key权限
        if (!hasApiKeyPermission("social_points:query")) {
            return ResultUtil.error(403, "API Key没有积分查询权限");
        }
        
        log.info("API Key调用积分查询接口，wxUnionId: {}", wxUnionId);
        
        return ResultUtil.data(socialPointsService.getMemberPointsHistoryVOByWxUnionId(wxUnionId));
    }

    /**
     * 检查当前API Key是否有指定权限
     */
    private boolean hasApiKeyPermission(String permission) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication == null || !"ROLE_API_KEY".equals(authentication.getAuthorities().iterator().next().getAuthority())) {
            log.warn("当前请求不是通过API Key认证的");
            return false;
        }

        String apiKey = (String) authentication.getPrincipal();
        return apiKeyService.hasPermission(apiKey, permission);
    }

    /**
     * 获取当前API Key信息
     */
    private ApiKey getCurrentApiKey() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication != null && authentication.getDetails() instanceof ApiKey) {
            return (ApiKey) authentication.getDetails();
        }
        
        return null;
    }
}
