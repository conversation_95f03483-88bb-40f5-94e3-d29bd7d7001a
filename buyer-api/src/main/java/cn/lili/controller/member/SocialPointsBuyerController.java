package cn.lili.controller.member;

import cn.lili.common.enums.ResultUtil;
import cn.lili.common.vo.PageVO;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.member.entity.vo.MemberPointsHistoryVO;
import cn.lili.modules.member.entity.vo.SocialPointsHistoryVO;
import cn.lili.modules.member.entity.vo.SocialPointsVO;
import cn.lili.modules.member.service.SocialPointsService;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 买家端,社交生态积分接口
 *
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
@RestController
@Api(tags = "买家端,社交生态积分接口")
@RequestMapping("/buyer/member/socialPoints")
public class SocialPointsBuyerController {
    @Autowired
    private SocialPointsService socialPointsService;

    @ApiOperation(value = "获取当前登录用户社交生态积分VO")
    @GetMapping(value = "/getCurrentMemberPoints")
    public ResultMessage<MemberPointsHistoryVO> getCurrentMemberPoints() {
        return ResultUtil.data(socialPointsService.getCurrentUserPoints());
    }

    @ApiOperation(value = "更新当前登录用户社交生态积分")
    @PostMapping(value = "/me")
    public ResultMessage<MemberPointsHistoryVO> updateCurrentMemberPoints(@Valid @RequestBody SocialPointsVO socialPoints) {
        // socialPointsService.updateCurrentUserPoints 方法会从 accessToken 解析出的当前用户信息中获取 wxUnionId
        // 小程序端在调用此接口时，SocialPointsVO 中可以不传递 wxUnionId
        return ResultUtil.data(socialPointsService.updateCurrentUserPoints(socialPoints));
    }

    /**
     * 注意：此接口如果直接由小程序等客户端调用，需要谨慎评估权限。
     * 通常情况下，客户端应只能修改自身用户的积分。
     * 若允许修改任意指定 wxUnionId 的用户积分，则调用方（accessToken对应的用户）需要有相应的高级权限。
     * 此接口更适合服务端之间的调用或后台管理系统使用。
     */
    @ApiOperation(value = "更新指定微信用户的积分VO (需要特定权限)")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "wxUnionId", value = "会员微信unionId", required = true, dataType = "String", paramType = "path"),
        @ApiImplicitParam(name = "socialPoints", value = "积分更新详情", required = true, dataTypeClass = SocialPointsVO.class, paramType = "body")
    })
    @PostMapping(value = "/updatePointsVOByWxUnionId/{wxUnionId}")
    public ResultMessage<MemberPointsHistoryVO> updatePointsVOByWxUnionId(
            @PathVariable String wxUnionId,
            @Valid @RequestBody SocialPointsVO socialPoints) {
        // 确保使用路径中的 wxUnionId
        socialPoints.setWxUnionId(wxUnionId);
        return ResultUtil.data(socialPointsService.updatePointsByWxUnionId(socialPoints));
    }

    @ApiOperation(value = "获取会员社交生态积分VO")
    @ApiImplicitParam(name = "wxUnionId", value = "会员微信unionid", dataType = "String", paramType = "path")
    @GetMapping(value = "/getMemberPointsByWx/{wxUnionId}")
    public ResultMessage<MemberPointsHistoryVO> getMemberPointsHistoryVOByWxUnionId(@PathVariable String wxUnionId) {
        return ResultUtil.data(socialPointsService.getMemberPointsHistoryVOByWxUnionId(wxUnionId));
    }

    @ApiOperation(value = "分页获取社交生态积分明细")
    @ApiImplicitParam(name = "wxUnionId", value = "会员微信unionid", dataType = "String", paramType = "path")
    @GetMapping(value = "/getMemberPointsHistoryByWx/{wxUnionId}")
    public ResultMessage<IPage<SocialPointsHistoryVO>> getByPageByWxUnionId(PageVO page, @PathVariable String wxUnionId) {
        return ResultUtil.data(socialPointsService.getMemberPointsHistoryByWx(page, wxUnionId));
    }
 
    @ApiOperation(value = "分页获取当前登录用户社交生态积分明细")
    @GetMapping(value = "/getCurrentUserPointsHistory")
    public ResultMessage<IPage<SocialPointsHistoryVO>> getCurrentUserPointsHistory(PageVO page) {
        return ResultUtil.data(socialPointsService.getCurrentUserPointsHistory(page));
    }
}
