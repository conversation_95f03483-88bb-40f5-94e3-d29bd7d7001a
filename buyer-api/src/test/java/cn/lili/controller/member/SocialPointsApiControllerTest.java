package cn.lili.controller.member;

import cn.lili.modules.member.entity.vo.MemberPointsHistoryVO;
import cn.lili.modules.member.entity.vo.SocialPointsVO;
import cn.lili.modules.member.service.SocialPointsService;
import cn.lili.modules.system.entity.dos.ApiKey;
import cn.lili.modules.system.entity.enums.ApiKeyStatusEnum;
import cn.lili.modules.system.service.ApiKeyService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * SocialPointsApiController 测试类
 */
@WebMvcTest(SocialPointsApiController.class)
public class SocialPointsApiControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SocialPointsService socialPointsService;

    @MockBean
    private ApiKeyService apiKeyService;

    @Autowired
    private ObjectMapper objectMapper;

    private ApiKey testApiKey;
    private MemberPointsHistoryVO testPointsVO;

    @BeforeEach
    void setUp() {
        // 设置测试用的API Key
        testApiKey = new ApiKey();
        testApiKey.setId("test-id");
        testApiKey.setName("测试API Key");
        testApiKey.setKeyValue("test-api-key");
        testApiKey.setSecret("test-secret");
        testApiKey.setStatus(ApiKeyStatusEnum.ACTIVE.name());
        testApiKey.setPermissions("{\"social_points:update\": true, \"social_points:query\": true}");

        // 设置测试用的积分VO
        testPointsVO = new MemberPointsHistoryVO();
        testPointsVO.setPoint("1100.00");
        testPointsVO.setTotalPoint("1100.00");
        testPointsVO.setFreezePoint("0.00");
    }

    @Test
    void testUpdatePointsByWxUnionId_Success() throws Exception {
        // Mock API Key验证
        when(apiKeyService.validateApiKey("test-api-key")).thenReturn(true);
        when(apiKeyService.getByKeyValue("test-api-key")).thenReturn(testApiKey);
        when(apiKeyService.hasPermission("test-api-key", "social_points:update")).thenReturn(true);

        // Mock 积分更新服务
        when(socialPointsService.updatePointsByWxUnionId(any(SocialPointsVO.class))).thenReturn(testPointsVO);

        SocialPointsVO requestVO = new SocialPointsVO();
        requestVO.setChangePoint("100.00");
        requestVO.setReason("测试奖励");

        mockMvc.perform(post("/buyer/api/member/socialPoints/updatePointsByWxUnionId/test-union-id")
                .header("X-API-Key", "test-api-key")
                .header("X-API-Secret", "test-secret")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.result.point").value("1100.00"));
    }

    @Test
    void testUpdatePointsByWxUnionId_InvalidApiKey() throws Exception {
        // Mock API Key验证失败
        when(apiKeyService.validateApiKey("invalid-api-key")).thenReturn(false);

        SocialPointsVO requestVO = new SocialPointsVO();
        requestVO.setChangePoint("100.00");
        requestVO.setReason("测试奖励");

        mockMvc.perform(post("/buyer/api/member/socialPoints/updatePointsByWxUnionId/test-union-id")
                .header("X-API-Key", "invalid-api-key")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestVO)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void testUpdatePointsByWxUnionId_NoPermission() throws Exception {
        // Mock API Key验证成功但无权限
        when(apiKeyService.validateApiKey("test-api-key")).thenReturn(true);
        when(apiKeyService.getByKeyValue("test-api-key")).thenReturn(testApiKey);
        when(apiKeyService.hasPermission("test-api-key", "social_points:update")).thenReturn(false);

        SocialPointsVO requestVO = new SocialPointsVO();
        requestVO.setChangePoint("100.00");
        requestVO.setReason("测试奖励");

        mockMvc.perform(post("/buyer/api/member/socialPoints/updatePointsByWxUnionId/test-union-id")
                .header("X-API-Key", "test-api-key")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestVO)))
                .andExpect(status().isForbidden());
    }

    @Test
    void testGetMemberPointsByWx_Success() throws Exception {
        // Mock API Key验证
        when(apiKeyService.validateApiKey("test-api-key")).thenReturn(true);
        when(apiKeyService.getByKeyValue("test-api-key")).thenReturn(testApiKey);
        when(apiKeyService.hasPermission("test-api-key", "social_points:query")).thenReturn(true);

        // Mock 积分查询服务
        when(socialPointsService.getMemberPointsHistoryVOByWxUnionId("test-union-id")).thenReturn(testPointsVO);

        mockMvc.perform(get("/buyer/api/member/socialPoints/getMemberPointsByWx/test-union-id")
                .header("X-API-Key", "test-api-key"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.result.point").value("1100.00"));
    }

    @Test
    void testUpdatePointsWithUnionId_Success() throws Exception {
        // Mock API Key验证
        when(apiKeyService.validateApiKey("test-api-key")).thenReturn(true);
        when(apiKeyService.getByKeyValue("test-api-key")).thenReturn(testApiKey);
        when(apiKeyService.hasPermission("test-api-key", "social_points:update")).thenReturn(true);

        // Mock 积分更新服务
        when(socialPointsService.updatePointsByWxUnionId(any(SocialPointsVO.class))).thenReturn(testPointsVO);

        SocialPointsVO requestVO = new SocialPointsVO();
        requestVO.setWxUnionId("test-union-id");
        requestVO.setChangePoint("50.00");
        requestVO.setReason("签到奖励");

        mockMvc.perform(post("/buyer/api/member/socialPoints/updatePointsWithUnionId")
                .header("X-API-Key", "test-api-key")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.result.point").value("1100.00"));
    }

    @Test
    void testUpdatePointsWithUnionId_MissingUnionId() throws Exception {
        // Mock API Key验证
        when(apiKeyService.validateApiKey("test-api-key")).thenReturn(true);
        when(apiKeyService.getByKeyValue("test-api-key")).thenReturn(testApiKey);
        when(apiKeyService.hasPermission("test-api-key", "social_points:update")).thenReturn(true);

        SocialPointsVO requestVO = new SocialPointsVO();
        requestVO.setChangePoint("50.00");
        requestVO.setReason("签到奖励");
        // 故意不设置wxUnionId

        mockMvc.perform(post("/buyer/api/member/socialPoints/updatePointsWithUnionId")
                .header("X-API-Key", "test-api-key")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestVO)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("wxUnionId不能为空"));
    }
}
